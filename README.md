# Lost & Found Portal

A comprehensive web-based Lost & Found Portal built with the MERN stack for university campuses. This application enables students and staff to report lost items, view found items, and connect securely to claim belongings.

## 🚀 Features

### Core Features
- **User Authentication**: Login/Signup using university email
- **Post Lost Item**: Form with item details, category, date lost, description, location, contact info, and image upload
- **Post Found Item**: Form with item details, category, image, location found, and description
- **Item Listing Pages**: Separate views for Lost Items and Found Items with filters by category, location, and date
- **Claim Request**: Users can request to claim a found item with notifications sent to finder
- **Dashboard**: Student dashboard (My Posts, My Requests), Admin dashboard (Approve/archive posts)
- **Real-Time Chat**: Secure communication between finder and claimer
- **Notification System**: Real-time notifications for item matches or updates
- **Responsive Design**: Clean UI for both mobile and desktop

### Advanced Features
- **Image Upload**: Multiple image support for items and claims
- **Search & Filter**: Advanced search with text search, category, location, and date filters
- **User Profiles**: User avatars, statistics, and profile management
- **Admin Panel**: User management, item verification, and platform statistics
- **Real-time Updates**: Socket.io integration for live notifications and chat

## 🛠️ Tech Stack

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose ODM
- **JWT** for authentication
- **Socket.io** for real-time features
- **Multer** for file uploads
- **Bcrypt** for password hashing
- **Helmet** for security
- **Rate limiting** for API protection

### Frontend
- **React 19** with TypeScript
- **Vite** for fast development
- **Material-UI (MUI)** for UI components
- **React Router** for navigation
- **TanStack Query** for data fetching
- **React Hook Form** with Yup validation
- **Socket.io Client** for real-time features
- **Axios** for API calls

## 📋 Prerequisites

- Node.js (v18 or higher)
- MongoDB Atlas account or local MongoDB installation
- Git

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd lost-and-found-portal
```

### 2. Backend Setup
```bash
cd backend
npm install
```

Create `.env` file in the backend directory:
```env
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:3000

# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/lost-and-found?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d

# Email Configuration (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# University Configuration
UNIVERSITY_EMAIL_DOMAIN=umt.edu.pk
UNIVERSITY_NAME=University of Management and Technology
```

### 3. Frontend Setup
```bash
cd frontend
npm install
```

The frontend `.env` file is already configured for local development.

### 4. MongoDB Setup

#### Option A: MongoDB Atlas (Recommended)
1. Create a free account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get your connection string
4. Replace the `MONGODB_URI` in your backend `.env` file

#### Option B: Local MongoDB
1. Install MongoDB locally
2. Start MongoDB service
3. Use `mongodb://localhost:27017/lost-and-found` as your `MONGODB_URI`

### 5. Run the Application

#### Development Mode
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev

# Or run both simultaneously from root
npm run dev
```

The application will be available at:
- Frontend: http://localhost:5173
- Backend API: http://localhost:5000
- API Health Check: http://localhost:5000/api/health

## 📁 Project Structure

```
lost-and-found-portal/
├── backend/                 # Node.js/Express API
│   ├── config/             # Database & environment config
│   ├── controllers/        # Business logic
│   ├── middleware/         # Auth & validation middleware
│   ├── models/             # MongoDB schemas
│   ├── routes/             # API endpoints
│   ├── uploads/            # File storage
│   └── server.js           # Main server file
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Main pages
│   │   ├── context/        # State management
│   │   ├── services/       # API calls
│   │   ├── utils/          # Helper functions
│   │   └── App.tsx         # Main app component
└── README.md
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update profile
- `PUT /api/auth/change-password` - Change password

### Items
- `GET /api/items` - Get all items (with filters)
- `GET /api/items/:id` - Get single item
- `POST /api/items` - Create new item
- `PUT /api/items/:id` - Update item
- `DELETE /api/items/:id` - Delete item
- `GET /api/items/user/my-items` - Get user's items

### Claims
- `POST /api/claims` - Create claim request
- `GET /api/claims/my-claims` - Get user's claims
- `GET /api/claims/received` - Get received claims
- `GET /api/claims/:id` - Get single claim
- `PUT /api/claims/:id/status` - Update claim status

### Chat
- `GET /api/chat` - Get user's chats
- `GET /api/chat/:id` - Get chat with messages
- `POST /api/chat` - Create new chat
- `POST /api/chat/:id/messages` - Send message

### Users
- `GET /api/users/dashboard` - Get dashboard stats
- `POST /api/users/avatar` - Upload avatar
- `GET /api/users/:id` - Get user profile

## 🔐 Default Admin Account

For testing purposes, you can create an admin account by:
1. Registering a normal account
2. Manually updating the user's role to 'admin' in the database
3. Or using the admin creation script (if implemented)

## 🧪 Testing

### Backend Testing
```bash
cd backend
npm test
```

### Frontend Testing
```bash
cd frontend
npm test
```

## 🚀 Deployment

### Backend Deployment (Heroku/Railway/DigitalOcean)
1. Set environment variables
2. Deploy the backend folder
3. Ensure MongoDB Atlas is accessible

### Frontend Deployment (Vercel/Netlify)
1. Build the frontend: `npm run build`
2. Deploy the `dist` folder
3. Update environment variables for production API URL

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -m 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For support, email <EMAIL> or create an issue in the repository.

## 🙏 Acknowledgments

- University of Management and Technology for the project requirements
- Material-UI for the excellent component library
- MongoDB Atlas for database hosting
- All contributors and testers
