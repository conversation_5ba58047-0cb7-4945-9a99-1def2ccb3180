import express from 'express';
import User from '../models/User.js';
import Item from '../models/Item.js';
import Claim from '../models/Claim.js';
import { protect, admin, staffOrAdmin } from '../middleware/auth.js';
import { uploadAvatar, handleUploadError, processUploadedFiles } from '../middleware/upload.js';

const router = express.Router();

// @desc    Get user dashboard stats
// @route   GET /api/users/dashboard
// @access  Private
router.get('/dashboard', protect, async (req, res) => {
  try {
    const userId = req.user._id;

    // Get user's items stats
    const itemsStats = await Item.aggregate([
      { $match: { postedBy: userId } },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          active: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          resolved: {
            $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] }
          }
        }
      }
    ]);

    // Get claims stats
    const claimsStats = await Claim.aggregate([
      {
        $match: {
          $or: [
            { claimant: userId },
            { itemOwner: userId }
          ]
        }
      },
      {
        $group: {
          _id: {
            role: {
              $cond: [{ $eq: ['$claimant', userId] }, 'claimant', 'owner']
            },
            status: '$status'
          },
          count: { $sum: 1 }
        }
      }
    ]);

    // Get recent activity
    const recentItems = await Item.find({ postedBy: userId })
      .sort('-createdAt')
      .limit(5)
      .select('title type status createdAt views');

    const recentClaims = await Claim.find({
      $or: [{ claimant: userId }, { itemOwner: userId }]
    })
      .populate('item', 'title type')
      .sort('-createdAt')
      .limit(5)
      .select('status createdAt');

    // Format stats
    const formattedItemsStats = itemsStats.reduce((acc, stat) => {
      acc[stat._id] = {
        total: stat.count,
        active: stat.active,
        resolved: stat.resolved
      };
      return acc;
    }, { lost: { total: 0, active: 0, resolved: 0 }, found: { total: 0, active: 0, resolved: 0 } });

    const formattedClaimsStats = claimsStats.reduce((acc, stat) => {
      if (!acc[stat._id.role]) acc[stat._id.role] = {};
      acc[stat._id.role][stat._id.status] = stat.count;
      return acc;
    }, { claimant: {}, owner: {} });

    res.json({
      success: true,
      data: {
        itemsStats: formattedItemsStats,
        claimsStats: formattedClaimsStats,
        recentActivity: {
          items: recentItems,
          claims: recentClaims
        }
      }
    });
  } catch (error) {
    console.error('Get dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching dashboard data'
    });
  }
});

// @desc    Upload user avatar
// @route   POST /api/users/avatar
// @access  Private
router.post('/avatar', protect, uploadAvatar, handleUploadError, processUploadedFiles, async (req, res) => {
  try {
    if (!req.uploadedFile) {
      return res.status(400).json({
        success: false,
        message: 'Please upload an image file'
      });
    }

    // Update user avatar
    const user = await User.findByIdAndUpdate(
      req.user._id,
      { avatar: req.uploadedFile.url },
      { new: true }
    );

    res.json({
      success: true,
      message: 'Avatar uploaded successfully',
      data: {
        avatar: user.avatar
      }
    });
  } catch (error) {
    console.error('Upload avatar error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while uploading avatar'
    });
  }
});

// @desc    Get platform statistics (Admin only)
// @route   GET /api/users/admin/stats
// @access  Private/Admin
router.get('/admin/stats', protect, admin, async (req, res) => {
  try {
    // User stats
    const userStats = await User.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 },
          active: {
            $sum: { $cond: ['$isActive', 1, 0] }
          }
        }
      }
    ]);

    // Item stats
    const itemStats = await Item.aggregate([
      {
        $group: {
          _id: {
            type: '$type',
            status: '$status'
          },
          count: { $sum: 1 }
        }
      }
    ]);

    // Claim stats
    const claimStats = await Claim.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Recent activity
    const recentUsers = await User.find()
      .sort('-createdAt')
      .limit(10)
      .select('name email role createdAt');

    res.json({
      success: true,
      data: {
        userStats,
        itemStats,
        claimStats,
        recentUsers
      }
    });
  } catch (error) {
    console.error('Get admin stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching statistics'
    });
  }
});

// @desc    Get all users (Admin only)
// @route   GET /api/users
// @access  Private/Admin
router.get('/', protect, admin, async (req, res) => {
  try {
    const {
      role,
      department,
      isActive,
      search,
      sortBy = '-createdAt',
      page = 1,
      limit = 20
    } = req.query;

    // Build query
    const query = {};

    if (role) query.role = role;
    if (department) query.department = new RegExp(department, 'i');
    if (isActive !== undefined) query.isActive = isActive === 'true';

    if (search) {
      query.$or = [
        { name: new RegExp(search, 'i') },
        { email: new RegExp(search, 'i') },
        { studentId: new RegExp(search, 'i') }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const users = await User.find(query)
      .select('-password')
      .sort(sortBy)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await User.countDocuments(query);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total
        }
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching users'
    });
  }
});

// @desc    Get user profile
// @route   GET /api/users/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('name email studentId department avatar stats createdAt');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get public stats
    const itemsCount = await Item.countDocuments({
      postedBy: req.params.id,
      status: 'active'
    });

    const resolvedCount = await Item.countDocuments({
      postedBy: req.params.id,
      status: 'resolved'
    });

    res.json({
      success: true,
      data: {
        user: {
          ...user.toObject(),
          publicStats: {
            itemsPosted: itemsCount,
            itemsResolved: resolvedCount
          }
        }
      }
    });
  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching user profile'
    });
  }
});

// @desc    Update user status (Admin only)
// @route   PUT /api/users/:id/status
// @access  Private/Admin
router.put('/:id/status', protect, admin, async (req, res) => {
  try {
    const { isActive } = req.body;

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'isActive must be a boolean value'
      });
    }

    const user = await User.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      data: { user }
    });
  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating user status'
    });
  }
});

export default router;
