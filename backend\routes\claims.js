import express from 'express';
import Claim from '../models/Claim.js';
import Item from '../models/Item.js';
import { protect, admin, staffOrAdmin } from '../middleware/auth.js';
import { uploadClaimProof, handleUploadError, processUploadedFiles, cleanupOnError } from '../middleware/upload.js';

const router = express.Router();

// Socket.io instance will be injected
let io;

// Function to set the socket.io instance
export const setSocketIO = (socketInstance) => {
  io = socketInstance;
};

// @desc    Create a claim request
// @route   POST /api/claims
// @access  Private
router.post('/', protect, uploadClaimProof, handleUploadError, processUploadedFiles, cleanupOnError, async (req, res) => {
  try {
    const { itemId, message, proofDescription, meetingLocation } = req.body;

    // Validation
    if (!itemId || !message) {
      return res.status(400).json({
        success: false,
        message: 'Item ID and message are required'
      });
    }

    // Check if item exists and is active
    const item = await Item.findById(itemId).populate('postedBy', 'name email');

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Item not found'
      });
    }

    if (item.status !== 'active') {
      return res.status(400).json({
        success: false,
        message: 'This item is no longer available for claims'
      });
    }

    // Check if user is trying to claim their own item
    if (item.postedBy._id.toString() === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'You cannot claim your own item'
      });
    }

    // Check if user has already claimed this item
    const existingClaim = await Claim.findOne({
      item: itemId,
      claimant: req.user._id,
      status: { $in: ['pending', 'approved'] }
    });

    if (existingClaim) {
      return res.status(400).json({
        success: false,
        message: 'You have already submitted a claim for this item'
      });
    }

    // Process proof images
    const proofImages = req.uploadedFiles ? req.uploadedFiles.map(file => ({
      url: file.url,
      publicId: null
    })) : [];

    // Create claim
    const claim = await Claim.create({
      item: itemId,
      claimant: req.user._id,
      itemOwner: item.postedBy._id,
      message,
      proofImages,
      proofDescription: proofDescription || '',
      meetingDetails: {
        location: meetingLocation || '',
        notes: ''
      }
    });

    // Add claim to item
    await Item.findByIdAndUpdate(itemId, {
      $push: { claims: claim._id }
    });

    // Populate the response
    await claim.populate([
      { path: 'claimant', select: 'name email studentId department' },
      { path: 'item', select: 'title type category' }
    ]);

    // Send real-time notification to item owner
    if (io) {
      io.to(item.postedBy._id.toString()).emit('newClaimRequest', {
        claimId: claim._id,
        itemTitle: item.title,
        claimantName: req.user.name,
        message: 'New claim request received'
      });
    }

    res.status(201).json({
      success: true,
      message: 'Claim request submitted successfully',
      data: { claim }
    });
  } catch (error) {
    console.error('Create claim error:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error while creating claim'
    });
  }
});

// @desc    Get user's claims (as claimant)
// @route   GET /api/claims/my-claims
// @access  Private
router.get('/my-claims', protect, async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;

    const query = { claimant: req.user._id };
    if (status) query.status = status;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const claims = await Claim.find(query)
      .populate('item', 'title type category images location dateOccurred')
      .populate('itemOwner', 'name email studentId')
      .sort('-createdAt')
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Claim.countDocuments(query);

    res.json({
      success: true,
      data: {
        claims,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total
        }
      }
    });
  } catch (error) {
    console.error('Get user claims error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching claims'
    });
  }
});

// @desc    Get claims for user's items (as item owner)
// @route   GET /api/claims/received
// @access  Private
router.get('/received', protect, async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;

    const query = { itemOwner: req.user._id };
    if (status) query.status = status;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const claims = await Claim.find(query)
      .populate('item', 'title type category images')
      .populate('claimant', 'name email studentId department')
      .sort('-createdAt')
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Claim.countDocuments(query);

    res.json({
      success: true,
      data: {
        claims,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total
        }
      }
    });
  } catch (error) {
    console.error('Get received claims error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching received claims'
    });
  }
});

// @desc    Get single claim
// @route   GET /api/claims/:id
// @access  Private
router.get('/:id', protect, async (req, res) => {
  try {
    const claim = await Claim.findById(req.params.id)
      .populate('item', 'title type category images location dateOccurred')
      .populate('claimant', 'name email studentId department phone')
      .populate('itemOwner', 'name email studentId department phone')
      .populate('timeline.performedBy', 'name');

    if (!claim) {
      return res.status(404).json({
        success: false,
        message: 'Claim not found'
      });
    }

    // Check if user is involved in this claim or is admin
    const isInvolved = claim.claimant._id.toString() === req.user._id.toString() ||
                      claim.itemOwner._id.toString() === req.user._id.toString() ||
                      req.user.role === 'admin';

    if (!isInvolved) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to view this claim'
      });
    }

    res.json({
      success: true,
      data: { claim }
    });
  } catch (error) {
    console.error('Get claim error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching claim'
    });
  }
});

// @desc    Update claim status (approve/reject)
// @route   PUT /api/claims/:id/status
// @access  Private (Item Owner or Admin)
router.put('/:id/status', protect, async (req, res) => {
  try {
    const { status, rejectionReason, meetingDetails } = req.body;

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be approved or rejected'
      });
    }

    const claim = await Claim.findById(req.params.id)
      .populate('item', 'title')
      .populate('claimant', 'name email');

    if (!claim) {
      return res.status(404).json({
        success: false,
        message: 'Claim not found'
      });
    }

    // Check if user is the item owner or admin
    if (claim.itemOwner.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this claim'
      });
    }

    if (claim.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Claim has already been processed'
      });
    }

    // Update claim
    claim.status = status;
    if (status === 'rejected' && rejectionReason) {
      claim.rejectionReason = rejectionReason;
    }
    if (status === 'approved' && meetingDetails) {
      claim.meetingDetails = { ...claim.meetingDetails, ...meetingDetails };
    }

    await claim.save();

    // Send notification to claimant
    if (io) {
      io.to(claim.claimant._id.toString()).emit('claimStatusUpdate', {
        claimId: claim._id,
        status,
        itemTitle: claim.item.title,
        message: `Your claim has been ${status}`
      });
    }

    res.json({
      success: true,
      message: `Claim ${status} successfully`,
      data: { claim }
    });
  } catch (error) {
    console.error('Update claim status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating claim status'
    });
  }
});

export default router;
