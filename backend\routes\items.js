import express from 'express';
import Item from '../models/Item.js';
import { protect, optionalAuth, admin, ownerOrAdmin } from '../middleware/auth.js';
import { uploadItemImages, handleUploadError, processUploadedFiles, cleanupOnError } from '../middleware/upload.js';

const router = express.Router();

// @desc    Get all items with filtering and pagination
// @route   GET /api/items
// @access  Public
router.get('/', optionalAuth, async (req, res) => {
  try {
    const {
      type,
      category,
      status = 'active',
      search,
      location,
      dateFrom,
      dateTo,
      sortBy = '-createdAt',
      page = 1,
      limit = 12
    } = req.query;

    // Build query
    const query = { status };

    if (type && ['lost', 'found'].includes(type)) {
      query.type = type;
    }

    if (category) {
      query.category = category;
    }

    if (location) {
      query['location.building'] = new RegExp(location, 'i');
    }

    if (dateFrom || dateTo) {
      query.dateOccurred = {};
      if (dateFrom) query.dateOccurred.$gte = new Date(dateFrom);
      if (dateTo) query.dateOccurred.$lte = new Date(dateTo);
    }

    if (search) {
      query.$text = { $search: search };
    }

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const items = await Item.find(query)
      .populate('postedBy', 'name email studentId department')
      .sort(sortBy)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Item.countDocuments(query);

    // Update view counts for authenticated users
    if (req.user) {
      const itemIds = items.map(item => item._id);
      await Item.updateMany(
        { _id: { $in: itemIds } },
        { $inc: { views: 1 } }
      );
    }

    res.json({
      success: true,
      data: {
        items,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
          hasNext: skip + items.length < total,
          hasPrev: parseInt(page) > 1
        }
      }
    });
  } catch (error) {
    console.error('Get items error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching items'
    });
  }
});

// @desc    Get user's items
// @route   GET /api/items/user/my-items
// @access  Private
router.get('/user/my-items', protect, async (req, res) => {
  try {
    const { type, status, page = 1, limit = 10 } = req.query;

    const query = { postedBy: req.user._id };
    if (type) query.type = type;
    if (status) query.status = status;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const items = await Item.find(query)
      .populate('claims')
      .sort('-createdAt')
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Item.countDocuments(query);

    res.json({
      success: true,
      data: {
        items,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total
        }
      }
    });
  } catch (error) {
    console.error('Get user items error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching user items'
    });
  }
});

// @desc    Get item categories and statistics
// @route   GET /api/items/stats/categories
// @access  Public
router.get('/stats/categories', async (req, res) => {
  try {
    const stats = await Item.aggregate([
      { $match: { status: 'active' } },
      {
        $group: {
          _id: {
            category: '$category',
            type: '$type'
          },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.category',
          lost: {
            $sum: {
              $cond: [{ $eq: ['$_id.type', 'lost'] }, '$count', 0]
            }
          },
          found: {
            $sum: {
              $cond: [{ $eq: ['$_id.type', 'found'] }, '$count', 0]
            }
          },
          total: { $sum: '$count' }
        }
      },
      { $sort: { total: -1 } }
    ]);

    res.json({
      success: true,
      data: { categories: stats }
    });
  } catch (error) {
    console.error('Get categories stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching statistics'
    });
  }
});

// @desc    Get single item
// @route   GET /api/items/:id
// @access  Public
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const item = await Item.findById(req.params.id)
      .populate('postedBy', 'name email studentId department phone avatar')
      .populate({
        path: 'claims',
        populate: {
          path: 'claimant',
          select: 'name email studentId'
        }
      });

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Item not found'
      });
    }

    // Increment view count
    if (req.user && req.user._id.toString() !== item.postedBy._id.toString()) {
      item.views += 1;
      await item.save();
    }

    res.json({
      success: true,
      data: { item }
    });
  } catch (error) {
    console.error('Get item error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching item'
    });
  }
});

// @desc    Create new item
// @route   POST /api/items
// @access  Private
router.post('/', protect, uploadItemImages, handleUploadError, processUploadedFiles, cleanupOnError, async (req, res) => {
  try {
    const {
      title,
      description,
      category,
      type,
      location,
      dateOccurred,
      contactInfo,
      priority = 'medium',
      metadata
    } = req.body;

    // Validation
    if (!title || !description || !category || !type || !location || !dateOccurred) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }

    // Process uploaded images
    const images = req.uploadedFiles ? req.uploadedFiles.map(file => ({
      url: file.url,
      publicId: null
    })) : [];

    if (images.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'At least one image is required'
      });
    }

    // Parse location if it's a string
    const parsedLocation = typeof location === 'string' ? JSON.parse(location) : location;
    const parsedContactInfo = typeof contactInfo === 'string' ? JSON.parse(contactInfo) : contactInfo;
    const parsedMetadata = metadata && typeof metadata === 'string' ? JSON.parse(metadata) : metadata;

    // Create item
    const item = await Item.create({
      title,
      description,
      category,
      type,
      images,
      location: parsedLocation,
      dateOccurred: new Date(dateOccurred),
      postedBy: req.user._id,
      contactInfo: {
        phone: parsedContactInfo?.phone || req.user.phone,
        email: parsedContactInfo?.email || req.user.email,
        preferredContact: parsedContactInfo?.preferredContact || 'both'
      },
      priority,
      metadata: parsedMetadata || {}
    });

    // Populate the response
    await item.populate('postedBy', 'name email studentId department');

    // Update user stats
    await req.user.updateOne({ $inc: { 'stats.itemsPosted': 1 } });

    res.status(201).json({
      success: true,
      message: 'Item posted successfully',
      data: { item }
    });
  } catch (error) {
    console.error('Create item error:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error while creating item'
    });
  }
});

// @desc    Update item
// @route   PUT /api/items/:id
// @access  Private (Owner or Admin)
router.put('/:id', protect, async (req, res) => {
  try {
    const item = await Item.findById(req.params.id);

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Item not found'
      });
    }

    // Check ownership or admin
    if (item.postedBy.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this item'
      });
    }

    const allowedUpdates = ['title', 'description', 'location', 'contactInfo', 'priority', 'metadata', 'status'];
    const updates = {};

    allowedUpdates.forEach(field => {
      if (req.body[field] !== undefined) {
        updates[field] = req.body[field];
      }
    });

    const updatedItem = await Item.findByIdAndUpdate(
      req.params.id,
      updates,
      { new: true, runValidators: true }
    ).populate('postedBy', 'name email studentId department');

    res.json({
      success: true,
      message: 'Item updated successfully',
      data: { item: updatedItem }
    });
  } catch (error) {
    console.error('Update item error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating item'
    });
  }
});

// @desc    Delete item
// @route   DELETE /api/items/:id
// @access  Private (Owner or Admin)
router.delete('/:id', protect, async (req, res) => {
  try {
    const item = await Item.findById(req.params.id);

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Item not found'
      });
    }

    // Check ownership or admin
    if (item.postedBy.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this item'
      });
    }

    await Item.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Item deleted successfully'
    });
  } catch (error) {
    console.error('Delete item error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting item'
    });
  }
});

export default router;
